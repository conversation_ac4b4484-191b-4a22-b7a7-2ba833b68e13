<!DOCTYPE html>
<html>
<head>
    <title>Element UI Tag Colors Test</title>
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
</head>
<body>
    <div id="app">
        <h3>Element UI Tag Colors</h3>
        <el-tag type="info">info类型 (OPEN状态)</el-tag>
        <el-tag type="danger">danger类型 (FIXED状态)</el-tag>
        <el-tag type="success">success类型 (CLOSED状态)</el-tag>
        <el-tag type="warning">warning类型</el-tag>
    </div>
    
    <script src="https://unpkg.com/vue@2/dist/vue.js"></script>
    <script src="https://unpkg.com/element-ui/lib/index.js"></script>
    <script>
        new Vue({
            el: '#app'
        })
        
        // 输出实际的颜色值
        setTimeout(() => {
            const tags = document.querySelectorAll('.el-tag');
            tags.forEach((tag, index) => {
                const computedStyle = window.getComputedStyle(tag);
                const bgColor = computedStyle.backgroundColor;
                console.log(`Tag ${index}: ${tag.textContent} - Background: ${bgColor}`);
            });
        }, 1000);
    </script>
</body>
</html>
